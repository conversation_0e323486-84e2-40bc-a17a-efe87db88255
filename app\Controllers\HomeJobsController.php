<?php

namespace App\Controllers;

class HomeJobsController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'info']);
        $this->session = session();
    }

    /**
     * Display all published job positions
     */
    public function index()
    {
        try {
            // Get filter parameters
            $selectedOrgId = $this->request->getGet('org_id');
            $searchTerm = $this->request->getGet('search');
            $selectedClassification = $this->request->getGet('classification');
            $selectedLocation = $this->request->getGet('location');
            $selectedPositionType = $this->request->getGet('position_type');

            // Load models
            $positionsModel = new \App\Models\PositionsModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Build the query for positions from published exercises
            $query = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code,
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.is_internal
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.status', 'active')
            ->where('exercises.status', 'published');

            // Apply filters
            if ($selectedOrgId) {
                $query->where('positions.org_id', $selectedOrgId);
            }

            if ($searchTerm) {
                $query->groupStart()
                     ->like('positions.designation', $searchTerm)
                     ->orLike('positions.position_reference', $searchTerm)
                     ->orLike('dakoii_org.org_name', $searchTerm)
                     ->orLike('exercises.exercise_name', $searchTerm)
                     ->groupEnd();
            }

            if ($selectedClassification) {
                $query->where('positions.classification', $selectedClassification);
            }

            if ($selectedLocation) {
                $query->like('positions.location', $selectedLocation);
            }

            if ($selectedPositionType) {
                if ($selectedPositionType === 'internal') {
                    $query->where('exercises.is_internal', 1);
                } elseif ($selectedPositionType === 'external') {
                    $query->where('exercises.is_internal', 0);
                }
            }

            // Get positions with pagination
            $positions = $query->orderBy('positions.created_at', 'DESC')
                              ->paginate(20); // 20 positions per page

            // Get all active organizations for filter dropdown
            $organizations = $orgModel->where('is_active', 1)
                                    ->orderBy('org_name', 'ASC')
                                    ->findAll();

            // Extract unique classifications and locations for filtering
            $allPositions = $positionsModel->select('classification, location')
                                          ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
                                          ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
                                          ->where('positions.status', 'active')
                                          ->where('exercises.status', 'published')
                                          ->findAll();

            $classifications = array_unique(array_filter(array_column($allPositions, 'classification')));
            $locations = array_unique(array_filter(array_column($allPositions, 'location')));

            // Get pagination object
            $pager = $positionsModel->pager;

            // Return the view with data
            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => $positions,
                'organizations' => $organizations,
                'classifications' => $classifications,
                'locations' => $locations,
                'pager' => $pager,
                'selectedOrgId' => $selectedOrgId,
                'searchTerm' => $searchTerm,
                'selectedClassification' => $selectedClassification,
                'selectedLocation' => $selectedLocation,
                'selectedPositionType' => $selectedPositionType
            ]);
        } catch (\Exception $e) {
            // Log the error and show empty positions
            log_message('error', 'Error loading jobs page: ' . $e->getMessage());

            return view('home/home_jobs', [
                'title' => 'Available Positions',
                'menu' => 'jobs',
                'positions' => [],
                'organizations' => [],
                'classifications' => [],
                'locations' => [],
                'pager' => null,
                'selectedOrgId' => null,
                'searchTerm' => null,
                'selectedClassification' => null,
                'selectedLocation' => null,
                'selectedPositionType' => null,
                'error' => 'Unable to load positions. Please try again later.'
            ]);
        }
    }

    public function view($id = null)
    {
        if ($id === null) {
            return redirect()->to('/jobs');
        }

        try {
            // Load models
            $positionsModel = new \App\Models\PositionsModel();

            // First try to get the position with minimal joins to see if it exists
            $position = $positionsModel->select('
                positions.*,
                dakoii_org.org_name,
                dakoii_org.org_code
            ')
            ->join('dakoii_org', 'positions.org_id = dakoii_org.id', 'left')
            ->where('positions.id', $id)
            ->where('positions.status', 'active')
            ->first();

            if ($position === null) {
                return redirect()->to('/jobs')->with('error', 'Position not found or not available.');
            }

            // Try to get additional exercise information if available
            $exerciseInfo = $positionsModel->select('
                positions_groups.group_name,
                positions_groups.exercise_id,
                exercises.exercise_name,
                exercises.advertisement_no,
                exercises.publish_date_from,
                exercises.publish_date_to,
                exercises.status as exercise_status,
                exercises.is_internal
            ')
            ->join('positions_groups', 'positions.position_group_id = positions_groups.id', 'left')
            ->join('exercises', 'positions_groups.exercise_id = exercises.id', 'left')
            ->where('positions.id', $id)
            ->first();

            // Merge exercise information if available
            if ($exerciseInfo) {
                $position = array_merge($position, $exerciseInfo);
            }

            return view('home/home_job_details', [
                'title' => $position['designation'],
                'menu' => 'jobs',
                'position' => $position
            ]);

        } catch (\Exception $e) {
            // Log the error
            log_message('error', 'Error loading position details: ' . $e->getMessage());

            return redirect()->to('/jobs')->with('error', 'Unable to load position details. Please try again later.');
        }
    }
}
