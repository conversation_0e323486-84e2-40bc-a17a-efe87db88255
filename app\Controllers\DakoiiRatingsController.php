<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use App\Models\RateItemsModel;
use App\Models\RateItemsScoresModel;

class DakoiiRatingsController extends ResourceController
{
    protected $itemsModel;
    protected $scoresModel;

    public function __construct()
    {
        $this->itemsModel = new RateItemsModel();
        $this->scoresModel = new RateItemsScoresModel();
    }

    /**
     * List all rating items
     *
     * @return mixed
     */
    public function index()
    {
        try {
            // Get real rating items from database
            $items = $this->itemsModel->getItemsWithScores();
        } catch (\Exception $e) {
            log_message('error', 'Rating items error: ' . $e->getMessage());
            $items = [];
        }

        $data = [
            'title' => 'Rating Items',
            'menu' => 'rating_items',
            'items' => $items
        ];

        return view('dakoii_ratings/dakoii_ratings_list', $data);
    }

    /**
     * Show form to create a new rating item
     *
     * @return mixed
     */
    public function new()
    {
        $data = [
            'title' => 'Add Rating Item',
            'menu' => 'rating_items'
        ];

        return view('dakoii_ratings/dakoii_ratings_create', $data);
    }

    /**
     * Create a new rating item
     *
     * @return mixed
     */
    public function create()
    {
        // Validate input
        $validation = $this->validate([
            'item_label' => 'required|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for insert
        $data = [
            'item_label' => $this->request->getPost('item_label'),
            'created_by' => session()->get('user_id') ?? 1
        ];

        // Insert the item
        if ($this->itemsModel->insert($data)) {
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create rating item');
        }
    }

    /**
     * Show form to edit a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function edit($id = null)
    {
        $item = $this->itemsModel->getItemById($id);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        $data = [
            'title' => 'Edit Rating Item',
            'menu' => 'rating_items',
            'item' => $item,
            'scores' => $this->scoresModel->getScoresByItemId($id)
        ];

        return view('dakoii_ratings/dakoii_ratings_edit', $data);
    }

    /**
     * Update a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function update($id = null)
    {
        // Validate input
        $validation = $this->validate([
            'item_label' => 'required|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for update
        $data = [
            'item_label' => $this->request->getPost('item_label'),
            'updated_by' => session()->get('user_id') ?? 1
        ];

        // Update the item
        if ($this->itemsModel->update($id, $data)) {
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update rating item');
        }
    }

    /**
     * Delete a rating item
     *
     * @param int $id
     * @return mixed
     */
    public function delete($id = null)
    {
        // Check if item exists
        $item = $this->itemsModel->getItemById($id);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        // Delete the item
        if ($this->itemsModel->delete($id)) {
            // When an item is deleted, its scores will be deleted automatically
            // due to foreign key constraints (if configured in the database)
            return redirect()->to('/dakoii/rating_items/list')->with('success', 'Rating item deleted successfully');
        } else {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Failed to delete rating item');
        }
    }

    /**
     * Show the add score form
     *
     * @param int $itemId
     * @return mixed
     */
    public function newScore($itemId = null)
    {
        $item = $this->itemsModel->getItemById($itemId);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        $data = [
            'title' => 'Add Rating Score',
            'menu' => 'rating_items',
            'item' => $item
        ];

        return view('dakoii_ratings/dakoii_ratings_scores_create', $data);
    }

    /**
     * Create a new score for an item
     *
     * @return mixed
     */
    public function createScore()
    {
        // Validate input
        $validation = $this->validate([
            'item_id' => 'required|numeric',
            'score' => 'required|numeric',
            'score_description' => 'permit_empty|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        // Prepare data for insert
        $data = [
            'item_id' => $this->request->getPost('item_id'),
            'score' => $this->request->getPost('score'),
            'score_description' => $this->request->getPost('score_description'),
            'created_by' => session()->get('user_id') ?? 1
        ];

        // Insert the score
        if ($this->scoresModel->insert($data)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $data['item_id'])->with('success', 'Rating score created successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to create rating score');
        }
    }

    /**
     * Show form to edit a score
     *
     * @param int $id
     * @return mixed
     */
    public function editScore($id = null)
    {
        $score = $this->scoresModel->getScoreById($id);

        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        $item = $this->itemsModel->getItemById($score['item_id']);

        $data = [
            'title' => 'Edit Rating Score',
            'menu' => 'rating_items',
            'score' => $score,
            'item' => $item
        ];

        return view('dakoii_ratings/dakoii_ratings_scores_edit', $data);
    }

    /**
     * Update a score
     *
     * @param int $id
     * @return mixed
     */
    public function updateScore($id = null)
    {
        // Validate input
        $validation = $this->validate([
            'score' => 'required|numeric',
            'score_description' => 'permit_empty|max_length[255]'
        ]);

        if (!$validation) {
            return redirect()->back()->withInput()->with('error', $this->validator->getErrors());
        }

        $score = $this->scoresModel->getScoreById($id);

        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        // Prepare data for update
        $data = [
            'score' => $this->request->getPost('score'),
            'score_description' => $this->request->getPost('score_description'),
            'updated_by' => session()->get('user_id') ?? 1
        ];

        // Update the score
        if ($this->scoresModel->update($id, $data)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $score['item_id'])->with('success', 'Rating score updated successfully');
        } else {
            return redirect()->back()->withInput()->with('error', 'Failed to update rating score');
        }
    }

    /**
     * Delete a score
     *
     * @param int $id
     * @return mixed
     */
    public function deleteScore($id = null)
    {
        $score = $this->scoresModel->getScoreById($id);

        if (empty($score)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating score not found');
        }

        $itemId = $score['item_id'];

        // Delete the score
        if ($this->scoresModel->delete($id)) {
            return redirect()->to('/dakoii/rating_items/edit/' . $itemId)->with('success', 'Rating score deleted successfully');
        } else {
            return redirect()->to('/dakoii/rating_items/edit/' . $itemId)->with('error', 'Failed to delete rating score');
        }
    }

    /**
     * Show import form
     *
     * @return mixed
     */
    public function import()
    {
        $data = [
            'title' => 'Import Rating Items',
            'menu' => 'rating_items'
        ];

        return view('dakoii_ratings/dakoii_ratings_import', $data);
    }

    /**
     * Download CSV template
     *
     * @return mixed
     */
    public function downloadTemplate()
    {
        // Create CSV content with headers and sample data
        $csvContent = "item_label,description\n";
        $csvContent .= "Communication Skills,Ability to communicate effectively\n";
        $csvContent .= "Technical Knowledge,Understanding of technical concepts\n";
        $csvContent .= "Leadership,Ability to lead and manage teams\n";

        // Set headers for file download
        $this->response->setHeader('Content-Type', 'text/csv');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="rating_items_template.csv"');
        $this->response->setHeader('Cache-Control', 'no-cache, must-revalidate');

        return $this->response->setBody($csvContent);
    }

    /**
     * Process CSV import
     *
     * @return mixed
     */
    public function processImport()
    {
        // Validate file upload
        $validation = $this->validate([
            'csv_file' => 'uploaded[csv_file]|ext_in[csv_file,csv]|max_size[csv_file,2048]'
        ]);

        if (!$validation) {
            return redirect()->back()->with('error', 'Please upload a valid CSV file (max 2MB)');
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            return redirect()->back()->with('error', 'Invalid file upload');
        }

        try {
            // Read CSV file
            $csvData = array_map('str_getcsv', file($file->getTempName()));
            $headers = array_shift($csvData); // Remove header row

            // Validate headers
            $expectedHeaders = ['item_label', 'description'];
            if ($headers !== $expectedHeaders) {
                return redirect()->back()->with('error', 'Invalid CSV format. Expected headers: ' . implode(', ', $expectedHeaders));
            }

            $imported = 0;
            $errors = [];
            $userId = session()->get('user_id') ?? 1;

            // Process each row
            foreach ($csvData as $rowIndex => $row) {
                $rowNumber = $rowIndex + 2; // +2 because we removed header and arrays are 0-indexed

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Validate required fields
                if (empty(trim($row[0]))) {
                    $errors[] = "Row {$rowNumber}: Item label is required";
                    continue;
                }

                // Check if item already exists
                if ($this->itemsModel->labelExists(trim($row[0]))) {
                    $errors[] = "Row {$rowNumber}: Item label '" . trim($row[0]) . "' already exists";
                    continue;
                }

                // Prepare data for insert
                $data = [
                    'item_label' => trim($row[0]),
                    'description' => isset($row[1]) ? trim($row[1]) : null,
                    'created_by' => $userId
                ];

                // Insert the item
                if ($this->itemsModel->insert($data)) {
                    $imported++;
                } else {
                    $errors[] = "Row {$rowNumber}: Failed to import item '" . trim($row[0]) . "'";
                }
            }

            // Prepare success/error messages
            $message = "Import completed. {$imported} items imported successfully.";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " errors occurred.";
                session()->setFlashdata('import_errors', $errors);
            }

            return redirect()->to('/dakoii/rating_items/list')->with('success', $message);

        } catch (\Exception $e) {
            log_message('error', 'CSV Import Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error processing CSV file: ' . $e->getMessage());
        }
    }

    /**
     * Show import form for scores
     *
     * @param int $itemId
     * @return mixed
     */
    public function importScores($itemId = null)
    {
        $item = $this->itemsModel->getItemById($itemId);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        $data = [
            'title' => 'Import Rating Scores',
            'menu' => 'rating_items',
            'item' => $item
        ];

        return view('dakoii_ratings/dakoii_ratings_scores_import', $data);
    }

    /**
     * Download CSV template for scores
     *
     * @param int $itemId
     * @return mixed
     */
    public function downloadScoresTemplate($itemId = null)
    {
        $item = $this->itemsModel->getItemById($itemId);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        // Create CSV content with headers and sample data
        $csvContent = "score,label,score_description\n";
        $csvContent .= "5,Excellent,Outstanding performance\n";
        $csvContent .= "4,Very Good,Above average performance\n";
        $csvContent .= "3,Good,Average performance\n";
        $csvContent .= "2,Fair,Below average performance\n";
        $csvContent .= "1,Poor,Unsatisfactory performance\n";

        // Set headers for file download
        $this->response->setHeader('Content-Type', 'text/csv');
        $this->response->setHeader('Content-Disposition', 'attachment; filename="rating_scores_template_' . $item['item_label'] . '.csv"');
        $this->response->setHeader('Cache-Control', 'no-cache, must-revalidate');

        return $this->response->setBody($csvContent);
    }

    /**
     * Process CSV import for scores
     *
     * @param int $itemId
     * @return mixed
     */
    public function processScoresImport($itemId = null)
    {
        $item = $this->itemsModel->getItemById($itemId);

        if (empty($item)) {
            return redirect()->to('/dakoii/rating_items/list')->with('error', 'Rating item not found');
        }

        // Validate file upload
        $validation = $this->validate([
            'csv_file' => 'uploaded[csv_file]|ext_in[csv_file,csv]|max_size[csv_file,2048]'
        ]);

        if (!$validation) {
            return redirect()->back()->with('error', 'Please upload a valid CSV file (max 2MB)');
        }

        $file = $this->request->getFile('csv_file');

        if (!$file->isValid()) {
            return redirect()->back()->with('error', 'Invalid file upload');
        }

        try {
            // Read CSV file
            $csvData = array_map('str_getcsv', file($file->getTempName()));
            $headers = array_shift($csvData); // Remove header row

            // Validate headers
            $expectedHeaders = ['score', 'label', 'score_description'];
            if ($headers !== $expectedHeaders) {
                return redirect()->back()->with('error', 'Invalid CSV format. Expected headers: ' . implode(', ', $expectedHeaders));
            }

            $imported = 0;
            $errors = [];
            $userId = session()->get('user_id') ?? 1;

            // Process each row
            foreach ($csvData as $rowIndex => $row) {
                $rowNumber = $rowIndex + 2; // +2 because we removed header and arrays are 0-indexed

                // Skip empty rows
                if (empty(array_filter($row))) {
                    continue;
                }

                // Validate required fields
                if (empty(trim($row[0]))) {
                    $errors[] = "Row {$rowNumber}: Score is required";
                    continue;
                }

                if (!is_numeric(trim($row[0]))) {
                    $errors[] = "Row {$rowNumber}: Score must be a number";
                    continue;
                }

                // Check if score already exists for this item
                $existingScore = $this->scoresModel->where('item_id', $itemId)
                                                  ->where('score', trim($row[0]))
                                                  ->first();
                if ($existingScore) {
                    $errors[] = "Row {$rowNumber}: Score '" . trim($row[0]) . "' already exists for this item";
                    continue;
                }

                // Prepare data for insert
                $data = [
                    'item_id' => $itemId,
                    'score' => trim($row[0]),
                    'label' => isset($row[1]) ? trim($row[1]) : null,
                    'score_description' => isset($row[2]) ? trim($row[2]) : null,
                    'created_by' => $userId
                ];

                // Insert the score
                if ($this->scoresModel->insert($data)) {
                    $imported++;
                } else {
                    $errors[] = "Row {$rowNumber}: Failed to import score '" . trim($row[0]) . "'";
                }
            }

            // Prepare success/error messages
            $message = "Import completed. {$imported} scores imported successfully.";
            if (!empty($errors)) {
                $message .= " " . count($errors) . " errors occurred.";
                session()->setFlashdata('import_errors', $errors);
            }

            return redirect()->to('/dakoii/rating_items/edit/' . $itemId)->with('success', $message);

        } catch (\Exception $e) {
            log_message('error', 'CSV Import Error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error processing CSV file: ' . $e->getMessage());
        }
    }
}