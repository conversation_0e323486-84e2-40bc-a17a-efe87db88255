<?php

namespace App\Services;

use App\Models\AppxApplicationProfileModel;
use App\Models\AppxApplicationDetailsModel;
use App\Models\AppxApplicationFilesModel;

/**
 * ProfileGenerationService
 * 
 * Service for generating comprehensive applicant profiles using AI
 */
class ProfileGenerationService
{
    protected $profileModel;
    protected $applicationModel;
    protected $filesModel;

    public function __construct()
    {
        $this->profileModel = new AppxApplicationProfileModel();
        $this->applicationModel = new AppxApplicationDetailsModel();
        $this->filesModel = new AppxApplicationFilesModel();
    }

    /**
     * Generate comprehensive profile using AI and insert into database
     *
     * @param int $applicationId
     * @param int $userId User ID performing the action
     * @return array Result with success status and message
     */
    public function generateAndInsertProfile($applicationId, $userId = null)
    {
        try {
            // Get application details with profile_details
            $application = $this->applicationModel->find($applicationId);
            
            if (!$application) {
                return [
                    'success' => false,
                    'message' => 'Application not found'
                ];
            }

            if (empty($application['profile_details'])) {
                return [
                    'success' => false,
                    'message' => 'No profile details available for AI processing'
                ];
            }

            // Check if profile already exists
            $existingProfile = $this->profileModel->getProfileByApplicationId($applicationId);
            if ($existingProfile) {
                return [
                    'success' => false,
                    'message' => 'Profile already exists for this application'
                ];
            }

            // Get application files for additional context
            $applicationFiles = $this->filesModel->getFilesByApplicationId($applicationId);
            
            // Prepare data for AI processing
            $profileData = $this->prepareProfileData($application, $applicationFiles);
            
            // Generate profile using AI
            log_message('info', 'Calling AI for profile generation for application: ' . $applicationId);
            $aiResponse = $this->generateProfileWithAI($profileData);

            if (!$aiResponse['success']) {
                log_message('error', 'AI profile generation failed for application: ' . $applicationId . ' - ' . $aiResponse['message']);
                return $aiResponse;
            }

            log_message('info', 'AI profile generation successful for application: ' . $applicationId);

            // Parse AI response and structure data for database
            $structuredProfile = $this->parseAIResponse($aiResponse['profile'], $application);
            
            // Insert profile into database
            $profileId = $this->insertProfile($structuredProfile, $applicationId, $userId);
            
            if ($profileId) {
                // Update application status
                $this->applicationModel->update($applicationId, [
                    'profile_status' => 'completed',
                    'profiled_by' => $userId,
                    'profiled_at' => date('Y-m-d H:i:s'),
                    'updated_by' => $userId
                ]);

                return [
                    'success' => true,
                    'message' => 'Profile generated and saved successfully',
                    'profile_id' => $profileId
                ];
            } else {
                return [
                    'success' => false,
                    'message' => 'Failed to save profile to database'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Profile generation error: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Error generating profile: ' . $e->getMessage()
            ];
        }
    }

    /**
     * Prepare profile data for AI processing
     *
     * @param array $application
     * @param array $applicationFiles
     * @return array
     */
    private function prepareProfileData($application, $applicationFiles = [])
    {
        // Parse profile_details JSON - handle both string and array cases
        $profileDetails = [];
        if (!empty($application['profile_details'])) {
            if (is_string($application['profile_details'])) {
                $profileDetails = json_decode($application['profile_details'], true);
                if (json_last_error() !== JSON_ERROR_NONE) {
                    log_message('warning', 'Failed to parse profile_details JSON: ' . json_last_error_msg());
                    $profileDetails = [];
                }
            } elseif (is_array($application['profile_details'])) {
                $profileDetails = $application['profile_details'];
            }
        }

        // Prepare comprehensive data structure
        $data = [
            'personal_information' => [
                'name' => trim($application['first_name'] . ' ' . $application['last_name']),
                'gender' => $application['gender'],
                'date_of_birth' => $application['date_of_birth'],
                'age' => $this->calculateAge($application['date_of_birth']),
                'place_of_origin' => $application['place_of_origin'],
                'current_address' => $application['location_address'],
                'citizenship' => $application['citizenship'],
                'marital_status' => $application['marital_status'],
                'contact_details' => $application['contact_details']
            ],
            'identification' => [
                'id_numbers' => $application['id_numbers']
            ],
            'employment_information' => [
                'current_employer' => $application['current_employer'],
                'current_position' => $application['current_position'],
                'current_salary' => $application['current_salary'],
                'is_public_servant' => $application['is_public_servant'],
                'public_service_file_number' => $application['public_service_file_number']
            ],
            'family_information' => [
                'children' => $application['children'],
                'spouse_employer' => $application['spouse_employer']
            ],
            'background_checks' => [
                'offence_convicted' => $application['offence_convicted']
            ],
            'references' => $application['referees'],
            'publications' => $application['publications'],
            'awards' => $application['awards'],
            'profile_details' => $profileDetails,
            'extracted_documents' => []
        ];

        // Add extracted text from files
        foreach ($applicationFiles as $file) {
            if (!empty($file['extracted_texts'])) {
                $data['extracted_documents'][] = [
                    'title' => $file['file_title'],
                    'description' => $file['file_description'],
                    'content' => $file['extracted_texts']
                ];
            }
        }

        return $data;
    }

    /**
     * Calculate age from date of birth
     *
     * @param string $dateOfBirth
     * @return int
     */
    private function calculateAge($dateOfBirth)
    {
        if (empty($dateOfBirth)) {
            return 0;
        }
        
        $birthDate = new \DateTime($dateOfBirth);
        $today = new \DateTime();
        return $today->diff($birthDate)->y;
    }

    /**
     * Generate profile using AI
     *
     * @param array $profileData
     * @return array
     */
    private function generateProfileWithAI($profileData)
    {
        // Load the Gemini AI helper
        helper('GeminiAI');
        
        // Create comprehensive prompt for profile generation
        $prompt = $this->createProfileGenerationPrompt();
        
        // Call Gemini AI
        $response = gemini_analyze_applicant($profileData, $prompt);

        // Handle the response format from gemini_analyze_applicant
        if (isset($response['success']) && $response['success']) {
            return [
                'success' => true,
                'profile' => $response['analysis'] // gemini_analyze_applicant returns 'analysis' key
            ];
        } else {
            return [
                'success' => false,
                'message' => $response['message'] ?? 'AI profile generation failed'
            ];
        }
    }

    /**
     * Create comprehensive prompt for AI profile generation
     *
     * @return string
     */
    private function createProfileGenerationPrompt()
    {
        return "You are a professional HR analyst tasked with creating a comprehensive applicant profile for government recruitment. 

Based on the provided applicant data, generate a detailed profile that includes the following structured information:

1. **Personal Summary**: A concise professional summary of the candidate
2. **Qualifications Analysis**: Detailed analysis of educational qualifications and certifications
3. **Professional Experience**: Comprehensive review of work experience and career progression
4. **Skills and Competencies**: Identification and assessment of key skills and competencies
5. **Knowledge Areas**: Analysis of specialized knowledge and expertise
6. **Publications and Research**: Review of any publications, research work, or academic contributions
7. **Awards and Recognition**: Analysis of awards, honors, and professional recognition
8. **References Assessment**: Summary of provided references and their relevance
9. **Strengths and Areas for Development**: Professional assessment of candidate strengths and potential areas for growth
10. **Overall Suitability**: Professional recommendation regarding the candidate's suitability for government positions

Please provide specific, factual information based only on the data provided. Format the response in clear sections with professional language suitable for HR review. Focus on:
- Professional competencies and qualifications
- Career progression and achievements
- Relevant skills and knowledge
- Overall professional profile assessment

Ensure the profile is comprehensive, professional, and suitable for government recruitment evaluation.";
    }

    /**
     * Parse AI response and structure data for database insertion
     *
     * @param string $aiProfile
     * @param array $application
     * @return array
     */
    private function parseAIResponse($aiProfile, $application)
    {
        // Extract key information from AI response for structured storage
        $structuredData = [
            'application_id' => $application['id'],
            'name' => trim($application['first_name'] . ' ' . $application['last_name']),
            'sex' => $application['gender'] ?? 'Not specified',
            'age' => $this->calculateAge($application['date_of_birth']),
            'place_origin' => $application['place_of_origin'] ?? 'Not specified',
            'contact_details' => $application['contact_details'] ?? '',
            'nid_number' => $this->extractNIDNumber($application['id_numbers']),
            'current_employer' => $application['current_employer'] ?? '',
            'current_position' => $application['current_position'] ?? '',
            'address_location' => $application['location_address'] ?? 'Not specified',
            'qualification_text' => $this->extractQualifications($aiProfile),
            'other_trainings' => $this->extractTrainings($aiProfile),
            'knowledge' => $this->extractKnowledge($aiProfile),
            'skills_competencies' => $this->extractSkillsCompetencies($aiProfile),
            'job_experiences' => $this->extractJobExperiences($aiProfile),
            'publications' => $application['publications'] ?? '',
            'awards' => $application['awards'] ?? '',
            'referees' => $application['referees'] ?? '',
            'comments' => $this->extractComments($aiProfile),
            'remarks' => $aiProfile // Store full AI-generated profile as remarks
        ];

        return $structuredData;
    }

    /**
     * Extract NID number from ID numbers string
     *
     * @param string $idNumbers
     * @return string
     */
    private function extractNIDNumber($idNumbers)
    {
        if (empty($idNumbers)) {
            return '';
        }

        // Parse JSON if it's JSON format
        $ids = json_decode($idNumbers, true);
        if (is_array($ids)) {
            return $ids['nid'] ?? $ids['national_id'] ?? '';
        }

        // If it's plain text, try to extract NID
        if (preg_match('/NID[:\s]*([A-Z0-9]+)/i', $idNumbers, $matches)) {
            return $matches[1];
        }

        return $idNumbers;
    }

    /**
     * Extract qualifications from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractQualifications($aiProfile)
    {
        // Look for qualifications section in AI response
        if (preg_match('/(?:qualifications?|education).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return 'Qualifications analysis available in full profile remarks.';
    }

    /**
     * Extract training information from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractTrainings($aiProfile)
    {
        // Look for training/certification sections
        if (preg_match('/(?:training|certification|course).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return '';
    }

    /**
     * Extract knowledge areas from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractKnowledge($aiProfile)
    {
        // Look for knowledge section
        if (preg_match('/(?:knowledge|expertise|specialization).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return 'Knowledge areas analysis available in full profile remarks.';
    }

    /**
     * Extract skills and competencies from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractSkillsCompetencies($aiProfile)
    {
        // Look for skills section
        if (preg_match('/(?:skills?|competenc).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return 'Skills and competencies analysis available in full profile remarks.';
    }

    /**
     * Extract job experiences from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractJobExperiences($aiProfile)
    {
        // Look for experience section
        if (preg_match('/(?:experience|employment|career).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return 'Job experience analysis available in full profile remarks.';
    }

    /**
     * Extract comments/summary from AI profile
     *
     * @param string $aiProfile
     * @return string
     */
    private function extractComments($aiProfile)
    {
        // Look for summary or overall assessment
        if (preg_match('/(?:summary|overall|assessment|recommendation).*?(?=\n\n|\n[A-Z]|$)/is', $aiProfile, $matches)) {
            return trim($matches[0]);
        }

        return 'Professional assessment available in full profile remarks.';
    }

    /**
     * Insert profile data into database
     *
     * @param array $profileData
     * @param int $applicationId
     * @param int $userId
     * @return int|false Profile ID on success, false on failure
     */
    private function insertProfile($profileData, $applicationId, $userId = null)
    {
        try {
            // Add metadata
            $profileData['created_by'] = $userId;
            $profileData['updated_by'] = $userId;

            // Insert profile
            $profileId = $this->profileModel->insert($profileData);

            if ($profileId) {
                log_message('info', "Profile generated and saved for application ID: {$applicationId}, Profile ID: {$profileId}");
                return $profileId;
            }

            return false;

        } catch (\Exception $e) {
            log_message('error', 'Error inserting profile: ' . $e->getMessage());
            return false;
        }
    }
}
