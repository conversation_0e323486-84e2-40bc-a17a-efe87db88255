<?= $this->extend('templates/dakoiiadmin') ?>

<?= $this->section('content') ?>

<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0">Import Rating Scores for "<?= esc($item['item_label']) ?>"</h5>
        <a href="<?= base_url('dakoii/rating_items/edit/' . $item['id']) ?>" class="btn btn-secondary btn-sm">
            <i class="fas fa-arrow-left"></i> Back to Item
        </a>
    </div>
    <div class="card-body">
        <!-- Import Instructions -->
        <div class="alert alert-info">
            <h6><i class="fas fa-info-circle"></i> Import Instructions</h6>
            <ul class="mb-0">
                <li>Download the CSV template below and fill in your data</li>
                <li>Required fields: <strong>score</strong></li>
                <li>Optional fields: <strong>label</strong>, <strong>score_description</strong></li>
                <li>Maximum file size: 2MB</li>
                <li>Duplicate scores for this item will be skipped</li>
                <li>Score must be a numeric value</li>
            </ul>
        </div>

        <!-- Download Template -->
        <div class="row mb-4">
            <div class="col-md-6">
                <div class="card border-primary">
                    <div class="card-body text-center">
                        <i class="fas fa-download fa-3x text-primary mb-3"></i>
                        <h6>Step 1: Download Template</h6>
                        <p class="text-muted">Download the CSV template with sample data</p>
                        <a href="<?= base_url('dakoii/rating_scores/download-template/' . $item['id']) ?>" class="btn btn-primary">
                            <i class="fas fa-download"></i> Download CSV Template
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card border-success">
                    <div class="card-body text-center">
                        <i class="fas fa-upload fa-3x text-success mb-3"></i>
                        <h6>Step 2: Upload Filled CSV</h6>
                        <p class="text-muted">Upload your completed CSV file</p>
                        <button type="button" class="btn btn-success" onclick="document.getElementById('uploadSection').scrollIntoView()">
                            <i class="fas fa-upload"></i> Upload CSV File
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- CSV Format Example -->
        <div class="card mb-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-table"></i> CSV Format Example</h6>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-bordered table-sm">
                        <thead class="table-light">
                            <tr>
                                <th>score</th>
                                <th>label</th>
                                <th>score_description</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>5</td>
                                <td>Excellent</td>
                                <td>Outstanding performance</td>
                            </tr>
                            <tr>
                                <td>4</td>
                                <td>Very Good</td>
                                <td>Above average performance</td>
                            </tr>
                            <tr>
                                <td>3</td>
                                <td>Good</td>
                                <td>Average performance</td>
                            </tr>
                            <tr>
                                <td>2</td>
                                <td>Fair</td>
                                <td>Below average performance</td>
                            </tr>
                            <tr>
                                <td>1</td>
                                <td>Poor</td>
                                <td>Unsatisfactory performance</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>

        <!-- Upload Section -->
        <div id="uploadSection" class="card">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-upload"></i> Upload CSV File</h6>
            </div>
            <div class="card-body">
                <form action="<?= base_url('dakoii/rating_scores/process-import/' . $item['id']) ?>" method="post" enctype="multipart/form-data">
                    <?= csrf_field() ?>
                    
                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV File</label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">Only CSV files are allowed (max 2MB)</div>
                    </div>

                    <div class="d-flex justify-content-between">
                        <a href="<?= base_url('dakoii/rating_items/edit/' . $item['id']) ?>" class="btn btn-secondary">
                            <i class="fas fa-times"></i> Cancel
                        </a>
                        <button type="submit" class="btn btn-success">
                            <i class="fas fa-upload"></i> Import CSV Data
                        </button>
                    </div>
                </form>
            </div>
        </div>

        <!-- Display Import Errors if any -->
        <?php if (session()->getFlashdata('import_errors')): ?>
            <div class="card mt-4">
                <div class="card-header bg-warning">
                    <h6 class="mb-0 text-dark"><i class="fas fa-exclamation-triangle"></i> Import Errors</h6>
                </div>
                <div class="card-body">
                    <div class="alert alert-warning">
                        <p><strong>The following errors occurred during import:</strong></p>
                        <ul class="mb-0">
                            <?php foreach (session()->getFlashdata('import_errors') as $error): ?>
                                <li><?= esc($error) ?></li>
                            <?php endforeach; ?>
                        </ul>
                    </div>
                </div>
            </div>
        <?php endif; ?>
    </div>
</div>

<script>
// File validation
document.getElementById('csv_file').addEventListener('change', function(e) {
    const file = e.target.files[0];
    if (file) {
        // Check file type
        if (!file.name.toLowerCase().endsWith('.csv')) {
            alert('Please select a CSV file');
            this.value = '';
            return;
        }
        
        // Check file size (2MB = 2 * 1024 * 1024 bytes)
        if (file.size > 2 * 1024 * 1024) {
            alert('File size must be less than 2MB');
            this.value = '';
            return;
        }
        
        // Show file info
        const fileInfo = document.createElement('div');
        fileInfo.className = 'alert alert-info mt-2';
        fileInfo.innerHTML = `<i class="fas fa-file-csv"></i> Selected: ${file.name} (${(file.size / 1024).toFixed(1)} KB)`;
        
        // Remove any existing file info
        const existingInfo = this.parentNode.querySelector('.alert-info');
        if (existingInfo) {
            existingInfo.remove();
        }
        
        this.parentNode.appendChild(fileInfo);
    }
});
</script>

<?= $this->endSection() ?>
