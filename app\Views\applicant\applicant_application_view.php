<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <a href="<?= base_url('applicant/applications') ?>" class="btn btn-outline-primary mb-3">
                        <i class="fas fa-arrow-left me-2"></i>Back to Applications
                    </a>
                    <h1 class="h3 mb-2">
                        Application Details
                        <span class="badge bg-primary ms-2"><?= esc($application['application_number']) ?></span>
                    </h1>
                    <p class="text-muted">
                        <strong>Status:</strong> 
                        <span class="badge bg-<?= get_status_color($application['application_status']) ?>">
                            <?= ucfirst(esc($application['application_status'])) ?>
                        </span>
                        <strong class="ms-3">Applied:</strong> <?= date('M d, Y', strtotime($application['created_at'])) ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- Application Overview -->
    <div class="row mb-4">
        <div class="col-lg-8">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>Position Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Position:</strong> <?= esc($position['designation']) ?></p>
                            <p><strong>Reference:</strong> <?= esc($position['position_reference']) ?></p>
                            <p><strong>Organization:</strong> <?= esc($organization['org_name']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <?php if ($exercise): ?>
                                <p><strong>Exercise:</strong> <?= esc($exercise['exercise_name']) ?></p>
                                <p><strong>Exercise Status:</strong> 
                                    <span class="badge bg-<?= $exercise['status'] === 'published' ? 'success' : 'warning' ?>">
                                        <?= ucfirst(esc($exercise['status'])) ?>
                                    </span>
                                </p>
                            <?php endif; ?>
                        </div>
                    </div>
                    
                    <?php if (!empty($position['job_description'])): ?>
                        <div class="mt-3">
                            <h6>Job Description:</h6>
                            <div class="text-muted">
                                <?= nl2br(esc($position['job_description'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
        
        <div class="col-lg-4">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-chart-bar me-2"></i>Application Summary
                    </h5>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <small class="text-muted">Total Experience</small>
                        <div class="fw-bold"><?= $totalExperience ?> years</div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Highest Education</small>
                        <div class="fw-bold"><?= esc($highestEducation) ?></div>
                    </div>
                    <div class="mb-3">
                        <small class="text-muted">Documents Submitted</small>
                        <div class="fw-bold"><?= count($files) ?> files</div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Personal Information -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-user me-2"></i>Personal Information
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-3">
                            <?php if (!empty($application['id_photo_path'])): ?>
                                <div class="text-center mb-3">
                                    <img src="<?= base_url($application['id_photo_path']) ?>" 
                                         alt="Profile Photo" 
                                         class="img-thumbnail" 
                                         style="max-width: 150px; max-height: 200px;">
                                </div>
                            <?php endif; ?>
                        </div>
                        <div class="col-md-9">
                            <div class="row">
                                <div class="col-md-6">
                                    <p><strong>Full Name:</strong> <?= esc($application['first_name'] . ' ' . $application['last_name']) ?></p>
                                    <p><strong>Gender:</strong> <?= esc($application['gender']) ?></p>
                                    <p><strong>Date of Birth:</strong> <?= $application['date_of_birth'] ? date('M d, Y', strtotime($application['date_of_birth'])) : 'Not specified' ?></p>
                                    <p><strong>Place of Origin:</strong> <?= esc($application['place_of_origin']) ?></p>
                                    <p><strong>Citizenship:</strong> <?= esc($application['citizenship']) ?></p>
                                </div>
                                <div class="col-md-6">
                                    <p><strong>Contact Details:</strong> <?= esc($application['contact_details']) ?></p>
                                    <p><strong>Address:</strong> <?= esc($application['location_address']) ?></p>
                                    <p><strong>Marital Status:</strong> <?= esc($application['marital_status']) ?></p>
                                    <?php if ($application['is_public_servant']): ?>
                                        <p><strong>Public Servant:</strong> Yes</p>
                                        <?php if (!empty($application['public_service_file_number'])): ?>
                                            <p><strong>File Number:</strong> <?= esc($application['public_service_file_number']) ?></p>
                                        <?php endif; ?>
                                    <?php endif; ?>
                                </div>
                            </div>
                            
                            <?php if (!empty($application['current_employer'])): ?>
                                <div class="mt-3 pt-3 border-top">
                                    <h6>Current Employment</h6>
                                    <div class="row">
                                        <div class="col-md-6">
                                            <p><strong>Employer:</strong> <?= esc($application['current_employer']) ?></p>
                                            <p><strong>Position:</strong> <?= esc($application['current_position']) ?></p>
                                        </div>
                                        <div class="col-md-6">
                                            <?php if (!empty($application['current_salary'])): ?>
                                                <p><strong>Current Salary:</strong> <?= esc($application['current_salary']) ?></p>
                                            <?php endif; ?>
                                        </div>
                                    </div>
                                </div>
                            <?php endif; ?>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Education History -->
    <?php if (!empty($education)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-graduation-cap me-2"></i>Education History
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Institution</th>
                                    <th>Course/Qualification</th>
                                    <th>Level</th>
                                    <th>Period</th>
                                    <th>Units</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($education as $edu): ?>
                                <tr>
                                    <td><?= esc($edu['institution']) ?></td>
                                    <td><?= esc($edu['course']) ?></td>
                                    <td>
                                        <span class="badge bg-info">
                                            <?= isset($educationLevels[$edu['education_level']]) ? esc($educationLevels[$edu['education_level']]) : 'Level ' . $edu['education_level'] ?>
                                        </span>
                                    </td>
                                    <td>
                                        <?= date('M Y', strtotime($edu['date_from'])) ?> - 
                                        <?= $edu['date_to'] ? date('M Y', strtotime($edu['date_to'])) : 'Present' ?>
                                    </td>
                                    <td><?= esc($edu['units']) ?></td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Work Experience -->
    <?php if (!empty($experiences)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-briefcase me-2"></i>Work Experience
                    </h5>
                </div>
                <div class="card-body">
                    <?php foreach ($experiences as $exp): ?>
                    <div class="border-bottom pb-3 mb-3">
                        <div class="row">
                            <div class="col-md-8">
                                <h6 class="mb-1"><?= esc($exp['position']) ?></h6>
                                <p class="text-muted mb-2"><?= esc($exp['employer']) ?></p>
                                <?php if (!empty($exp['employer_contacts_address'])): ?>
                                    <p class="small text-muted mb-2"><?= esc($exp['employer_contacts_address']) ?></p>
                                <?php endif; ?>
                            </div>
                            <div class="col-md-4 text-md-end">
                                <span class="badge bg-secondary">
                                    <?= date('M Y', strtotime($exp['date_from'])) ?> - 
                                    <?= $exp['date_to'] ? date('M Y', strtotime($exp['date_to'])) : 'Present' ?>
                                </span>
                            </div>
                        </div>
                        <?php if (!empty($exp['work_description'])): ?>
                            <div class="mt-2">
                                <strong>Description:</strong>
                                <p class="text-muted mb-1"><?= nl2br(esc($exp['work_description'])) ?></p>
                            </div>
                        <?php endif; ?>
                        <?php if (!empty($exp['achievements'])): ?>
                            <div class="mt-2">
                                <strong>Achievements:</strong>
                                <p class="text-muted mb-1"><?= nl2br(esc($exp['achievements'])) ?></p>
                            </div>
                        <?php endif; ?>
                    </div>
                    <?php endforeach; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Supporting Documents -->
    <?php if (!empty($files)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-file-alt me-2"></i>Supporting Documents
                    </h5>
                </div>
                <div class="card-body">
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Document Title</th>
                                    <th>Description</th>
                                    <th>Uploaded</th>
                                    <th>Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($files as $file): ?>
                                <tr>
                                    <td>
                                        <i class="fas fa-file-pdf text-danger me-2"></i>
                                        <?= esc($file['file_title']) ?>
                                    </td>
                                    <td><?= esc($file['file_description']) ?></td>
                                    <td><?= date('M d, Y', strtotime($file['created_at'])) ?></td>
                                    <td>
                                        <?php if (!empty($file['file_path']) && file_exists(FCPATH . $file['file_path'])): ?>
                                            <a href="<?= base_url($file['file_path']) ?>" 
                                               target="_blank" 
                                               class="btn btn-sm btn-outline-primary">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                        <?php else: ?>
                                            <span class="text-muted">File not available</span>
                                        <?php endif; ?>
                                    </td>
                                </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- AI Generated Profile -->
    <?php if (!empty($profileDetails)): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-brain me-2"></i>AI Generated Profile Analysis
                    </h5>
                </div>
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <p class="text-muted mb-0">Comprehensive profile analysis generated using AI</p>
                        <button type="button" class="btn btn-sm btn-outline-primary" id="toggleProfileBtn">
                            <i class="fas fa-eye me-1"></i>View Profile
                        </button>
                    </div>

                    <div id="profileContent" style="display: none;">
                        <div class="border rounded p-3 bg-light">
                            <pre class="mb-0" style="white-space: pre-wrap; font-size: 0.9em; max-height: 500px; overflow-y: auto; font-family: 'Courier New', monospace;"><?= json_encode($profileDetails, JSON_PRETTY_PRINT | JSON_UNESCAPED_SLASHES) ?></pre>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <!-- Additional Information -->
    <?php if (!empty($application['referees']) || !empty($application['publications']) || !empty($application['awards'])): ?>
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">
                        <i class="fas fa-info-circle me-2"></i>Additional Information
                    </h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($application['referees'])): ?>
                        <div class="mb-4">
                            <h6>Referees</h6>
                            <div class="text-muted">
                                <?= nl2br(esc($application['referees'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($application['publications'])): ?>
                        <div class="mb-4">
                            <h6>Publications</h6>
                            <div class="text-muted">
                                <?= nl2br(esc($application['publications'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>

                    <?php if (!empty($application['awards'])): ?>
                        <div class="mb-4">
                            <h6>Awards & Recognition</h6>
                            <div class="text-muted">
                                <?= nl2br(esc($application['awards'])) ?>
                            </div>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
    <?php endif; ?>
</div>

<?= $this->endSection() ?>

<?= $this->section('styles') ?>
<style>
.card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
    border: 1px solid rgba(0, 0, 0, 0.125);
}

.card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
}

.badge {
    font-size: 0.75em;
}

.table th {
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.img-thumbnail {
    border: 2px solid #dee2e6;
}

.border-bottom:last-child {
    border-bottom: none !important;
}

#profileContent pre {
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
    color: #495057;
}
</style>
<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Handle toggle profile view
    $('#toggleProfileBtn').on('click', function() {
        const content = $('#profileContent');
        const btn = $(this);

        if (content.is(':visible')) {
            content.hide();
            btn.html('<i class="fas fa-eye me-1"></i>View Profile');
        } else {
            content.show();
            btn.html('<i class="fas fa-eye-slash me-1"></i>Hide Profile');
        }
    });
});
</script>
<?= $this->endSection() ?>
