<?php

namespace App\Controllers;

use CodeIgniter\API\ResponseTrait;

class IncomingApplicantsController extends BaseController
{
    use ResponseTrait;

    protected $session;
    protected $applicationsModel;

    public function __construct()
    {
        helper(['form', 'url']);
        $this->session = \Config\Services::session();
        $this->applicationsModel = new \App\Models\AppxApplicationDetailsModel();
    }

    /**
     * Display the list of unacknowledged applications
     */
    public function index()
    {
        // Get current user's organization ID from session
        $orgId = session()->get('org_id');

        // Get real applications data from database using model method, filtered by organization
        $applications = $this->applicationsModel->getUnacknowledgedApplicationsWithDetails($orgId);

        // Format contact details for display
        foreach ($applications as &$application) {
            // Parse contact_details JSON if it exists
            if (!empty($application['contact_details'])) {
                $contacts = json_decode($application['contact_details'], true);
                if (is_array($contacts)) {
                    $application['email'] = $contacts['email'] ?? '';
                    $application['phone'] = $contacts['phone'] ?? '';
                    $application['contact_display'] = $contacts['phone'] ?? '';
                } else {
                    // If it's not JSON, treat as plain text
                    $application['contact_display'] = $application['contact_details'];
                    $application['email'] = '';
                    $application['phone'] = '';
                }
            } else {
                $application['contact_display'] = '';
                $application['email'] = '';
                $application['phone'] = '';
            }

            // Set display names for compatibility with view
            $application['fname'] = $application['first_name'];
            $application['lname'] = $application['last_name'];
            $application['recieved_acknowledged'] = $application['received_at'];

            // Set department from organization name if not available
            $application['department'] = $application['org_name'] ?? 'N/A';
        }

        $data = [
            'title' => 'Incoming Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('incoming_applicants/incoming_applicants_index', $data);
    }

    /**
     * Mark an application as received/acknowledged and send email notification
     */
    public function acknowledge($id)
    {
        $userId = $this->session->get('user_id') ?? 1;

        $data = [
            'is_received' => 1,
            'received_status' => 'acknowledged',
            'received_by' => $userId,
            'received_at' => date('Y-m-d H:i:s'),
            'application_status' => 'active',
            'updated_by' => $userId
        ];

        try {
            // Update the application in database
            $updateSuccess = $this->applicationsModel->update($id, $data);

            if ($updateSuccess) {
                log_message('info', 'Application acknowledged: ID ' . $id);

                // Get current user's organization ID from session
                $orgId = session()->get('org_id');

                // Get application details with full data, filtered by organization
                $application = $this->applicationsModel->getApplicationWithDetails($id, $orgId);

                if (!$application) {
                    return $this->response->setJSON([
                        'success' => false,
                        'message' => 'Application not found after acknowledgment or access denied',
                        'csrf_hash' => csrf_hash()
                    ]);
                }

                // Send acknowledgment email
                $emailResult = $this->sendAcknowledgmentEmail($application);

                // Return result
                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'Application successfully acknowledged and email sent',
                    'email_sent' => $emailResult['success'],
                    'email_message' => $emailResult['message'],
                    'csrf_hash' => csrf_hash()
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Failed to acknowledge application',
                    'csrf_hash' => csrf_hash()
                ]);
            }
        } catch (\Exception $e) {
            log_message('error', 'Error acknowledging application: ' . $e->getMessage());
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error: ' . $e->getMessage(),
                'csrf_hash' => csrf_hash()
            ]);
        }
    }

    /**
     * Send acknowledgment email to applicant
     */
    private function sendAcknowledgmentEmail($application)
    {
        try {
            // Get applicant email from ApplicantsModel using applicant_id
            $applicantsModel = new \App\Models\ApplicantsModel();
            $applicant = $applicantsModel->find($application['applicant_id']);

            if (!$applicant || empty($applicant['email'])) {
                return [
                    'success' => false,
                    'message' => 'No email address found for applicant'
                ];
            }

            $applicantEmail = $applicant['email'];

            // Get organization details
            $orgModel = new \App\Models\DakoiiOrgModel();
            $organization = $orgModel->find($application['org_id']);

            if (!$organization) {
                return [
                    'success' => false,
                    'message' => 'Organization not found'
                ];
            }

            // Generate email content with all required information
            $emailContent = "
                <div style='font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; background: linear-gradient(135deg, #dc3545 0%, #000000 100%); color: white; padding: 0; border-radius: 10px; overflow: hidden;'>
                    <div style='background: rgba(255,255,255,0.1); padding: 30px; text-align: center; border-bottom: 2px solid rgba(255,215,0,0.3);'>
                        <h1 style='margin: 0; color: #ffd700; font-size: 28px; font-weight: bold;'>DERS</h1>
                        <p style='margin: 5px 0 0 0; font-size: 14px; opacity: 0.9;'>Dakoii Echad Recruitment & Selection System</p>
                    </div>

                    <div style='padding: 30px;'>
                        <h2 style='color: #ffd700; margin-top: 0;'>Application Receipt Acknowledgment</h2>

                        <p>Dear " . trim($application['first_name'] . ' ' . $application['last_name']) . ",</p>

                        <p>We acknowledge receipt of your application for the following position:</p>

                        <div style='background: rgba(255,255,255,0.1); padding: 15px; margin: 20px 0; border-radius: 5px;'>
                            <h3 style='color: #ffd700; margin: 0 0 10px 0;'>Exercise & Advertisement Details:</h3>
                            <strong>Gazzetted Number:</strong> " . ($application['gazzetted_no'] ?? 'N/A') . "<br>
                            <strong>Gazzetted Date:</strong> " . ($application['gazzetted_date'] ? date('d M Y', strtotime($application['gazzetted_date'])) : 'N/A') . "<br>
                            <strong>Advertisement Number:</strong> " . ($application['advertisement_no'] ?? 'N/A') . "<br>
                            <strong>Advertisement Date:</strong> " . ($application['advertisement_date'] ? date('d M Y', strtotime($application['advertisement_date'])) : 'N/A') . "<br>
                            <strong>Advertisement Type:</strong> " . ($application['is_internal'] ? 'Internal' : 'External') . "<br>
                            <strong>Publish Date From:</strong> " . ($application['publish_date_from'] ? date('d M Y', strtotime($application['publish_date_from'])) : 'N/A') . "<br>
                            <strong>Publish Date To:</strong> " . ($application['publish_date_to'] ? date('d M Y', strtotime($application['publish_date_to'])) : 'N/A') . "
                        </div>

                        <div style='background: rgba(255,255,255,0.1); padding: 15px; margin: 20px 0; border-radius: 5px;'>
                            <h3 style='color: #ffd700; margin: 0 0 10px 0;'>Position & Organization Details:</h3>
                            <strong>Organization Name:</strong> " . $organization['org_name'] . "<br>
                            <strong>Position Reference:</strong> " . $application['position_reference'] . "<br>
                            <strong>Position Name:</strong> " . $application['position_title'] . "
                        </div>

                        <div style='background: rgba(255,255,255,0.1); padding: 15px; margin: 20px 0; border-radius: 5px;'>
                            <h3 style='color: #ffd700; margin: 0 0 10px 0;'>Application Details:</h3>
                            <strong>Application Number:</strong> " . $application['application_number'] . "<br>
                            <strong>Date Received:</strong> " . date('d M Y, h:i A') . "
                        </div>

                        <p>Your application is now being processed. You will be notified of any updates regarding your application status.</p>

                        <p>Thank you for your interest in this position.</p>

                        <p style='margin-top: 30px;'>
                            Best Regards<br><br>
                            <strong style='color: #ffd700;'>Selection Committee</strong><br>
                            <strong style='color: #ffd700;'>" . $organization['org_name'] . "</strong>
                        </p>
                    </div>
                </div>
            ";

            // Send email using CodeIgniter email service
            $emailService = \Config\Services::email();

            // Configure email settings
            $config = [
                'protocol' => 'smtp',
                'SMTPHost' => 'mail.dakoiims.com',
                'SMTPUser' => '<EMAIL>',
                'SMTPPass' => 'dakoiianzii',
                'SMTPPort' => 465,
                'SMTPCrypto' => 'ssl',
                'mailType' => 'html',
                'charset' => 'utf-8',
                'wordWrap' => true,
                'newline' => "\r\n"
            ];

            $emailService->initialize($config);

            // Set sender
            $emailService->setFrom('<EMAIL>', 'DERS - Dakoii Echad Recruitment & Selection System');

            // Set reply-to using organization email
            if (!empty($organization['email_addresses'])) {
                $orgEmails = explode(',', $organization['email_addresses']);
                if (!empty($orgEmails)) {
                    $emailService->setReplyTo(trim($orgEmails[0]), $organization['org_name']);
                }
            }

            // Set recipient and content
            $emailService->setTo($applicantEmail);
            $emailService->setSubject('Application Receipt Acknowledgment - ' . $application['application_number']);
            $emailService->setMessage($emailContent);

            // Send email
            $result = $emailService->send();

            if ($result) {
                log_message('info', 'Acknowledgment email sent successfully to: ' . $applicantEmail);
                return [
                    'success' => true,
                    'message' => 'Email sent successfully'
                ];
            } else {
                $debugInfo = $emailService->printDebugger();
                log_message('error', 'Failed to send email: ' . $debugInfo);
                return [
                    'success' => false,
                    'message' => 'Failed to send email'
                ];
            }

        } catch (\Exception $e) {
            log_message('error', 'Exception while sending email: ' . $e->getMessage());
            return [
                'success' => false,
                'message' => 'Exception while sending email: ' . $e->getMessage()
            ];
        }
    }


    /**
     * View application details
     */
    public function view($id)
    {
        // Get current user's organization ID from session
        $orgId = session()->get('org_id');

        // Get real application data from database using model method, filtered by organization
        $application = $this->applicationsModel->getApplicationWithDetails($id, $orgId);

        if (!$application) {
            return redirect()->to('incoming_applications')->with('error', 'Application not found or access denied');
        }

        // Parse contact details for display
        if (!empty($application['contact_details'])) {
            $contacts = json_decode($application['contact_details'], true);
            if (is_array($contacts)) {
                $application['email'] = $contacts['email'] ?? '';
                $application['phone'] = $contacts['phone'] ?? '';
                $application['contact_display'] = $contacts['phone'] ?? '';
            } else {
                $application['contact_display'] = $application['contact_details'];
                $application['email'] = '';
                $application['phone'] = '';
            }
        } else {
            $application['contact_display'] = '';
            $application['email'] = '';
            $application['phone'] = '';
        }

        // Set display names for compatibility with view
        $application['fname'] = $application['first_name'];
        $application['lname'] = $application['last_name'];
        $application['recieved_acknowledged'] = $application['received_at'];
        $application['dobirth'] = $application['date_of_birth'];

        // Set department from organization name
        $application['department'] = $application['org_name'] ?? 'N/A';

        // Get application files
        $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
        $applicationFiles = $appxFilesModel->where('application_id', $id)->findAll();

        // Set file paths for compatibility and organize files
        foreach ($applicationFiles as $file) {
            if (strpos($file['file_title'], 'CV') !== false || strpos($file['file_title'], 'Resume') !== false) {
                $application['cv_path'] = $file['file_path'];
            } elseif (strpos($file['file_title'], 'Cover') !== false) {
                $application['cover_letter_path'] = $file['file_path'];
            }
        }

        $data = [
            'title' => 'Application Details - ' . $application['application_number'],
            'menu' => 'applications',
            'application' => $application,
            'applicationFiles' => $applicationFiles
        ];

        return view('incoming_applicants/incoming_applicants_view', $data);
    }

    /**
     * Batch acknowledge multiple applications
     */
    public function batchAcknowledge()
    {
        $ids = $this->request->getPost('ids');

        if (empty($ids)) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'No applications selected',
                'csrf_hash' => csrf_hash()
            ]);
        }

        $data = [
            'is_received' => 1,
            'received_status' => 'acknowledged',
            'received_by' => $this->session->get('user_id') ?? 1,
            'received_at' => date('Y-m-d H:i:s'),
            'application_status' => 'active',
            'updated_by' => $this->session->get('user_id') ?? 1
        ];

        $successCount = 0;
        $failCount = 0;
        $emailSentCount = 0;

        foreach ($ids as $id) {
            try {
                // Update the application in database
                $updateSuccess = $this->applicationsModel->update($id, $data);

                if ($updateSuccess) {
                    $successCount++;

                    // Get current user's organization ID from session
                    $orgId = session()->get('org_id');

                    // Get application data for email notification, filtered by organization
                    $application = $this->applicationsModel->getApplicationWithDetails($id, $orgId);

                    if ($application) {
                        // Send acknowledgment email
                        $emailResult = $this->sendAcknowledgmentEmail($application);
                        if ($emailResult['success']) $emailSentCount++;
                    }
                } else {
                    $failCount++;
                }
            } catch (\Exception $e) {
                log_message('error', 'Error in batch acknowledge for ID ' . $id . ': ' . $e->getMessage());
                $failCount++;
            }
        }

        return $this->response->setJSON([
            'success' => true,
            'message' => "$successCount applications acknowledged successfully. $failCount failed. $emailSentCount email notifications sent.",
            'csrf_hash' => csrf_hash()
        ]);
    }


}