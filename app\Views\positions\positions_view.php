<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">Positions List</h5>
                    <p class="mb-0 text-muted">View and manage positions in <?= esc($group['group_name']) ?></p>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('positions/positions_groups/' . $group['exercise_id']) ?>" class="btn btn-outline-secondary me-2">
                        <i class="fas fa-arrow-left me-2"></i>Back to Position Groups
                    </a>
                    <div class="btn-group me-2" role="group">
                        <button type="button" class="btn btn-success" data-bs-toggle="modal" data-bs-target="#importModal">
                            <i class="fas fa-file-csv me-2"></i>Import CSV
                        </button>
                        <a href="<?= base_url('positions/download-csv-template/' . $group['id']) ?>" class="btn btn-outline-success">
                            <i class="fas fa-download me-2"></i>Download Template
                        </a>
                    </div>
                    <a href="<?= base_url('positions/create?group_id=' . $group['id']) ?>" class="btn btn-primary">
                        <i class="fas fa-plus me-2"></i>Add Position
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <div class="table-responsive">
                <table class="table table-hover" id="positionsTable">
                    <thead>
                        <tr>
                            <th>Reference</th>
                            <th>Designation</th>
                            <th>Classification</th>
                            <th>Award</th>
                            <th>Location</th>
                            <th>Annual Salary</th>
                            <th>Status</th>
                            <th>JD</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php foreach ($positions as $position): ?>
                        <tr>
                            <td><?= esc($position['position_reference']) ?></td>
                            <td><?= esc($position['designation']) ?></td>
                            <td><?= esc($position['classification']) ?></td>
                            <td><?= esc($position['award']) ?></td>
                            <td><?= esc($position['location']) ?></td>
                            <td><?= esc($position['annual_salary']) ?></td>
                            <td><?= esc($position['status']) ?></td>
                            <td>
                                <?php if (!empty($position['jd_filepath'])): ?>
                                    <a href="<?= base_url($position['jd_filepath']) ?>" class="btn btn-sm btn-primary" target="_blank" title="Download Job Description">
                                        <i class="fas fa-file-pdf"></i>
                                    </a>
                                <?php else: ?>
                                    <span class="badge bg-secondary">No JD</span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <a href="<?= base_url('positions/show/' . $position['id']) ?>" class="btn btn-sm btn-info" title="View Position">
                                    <i class="fas fa-eye"></i>
                                </a>
                                <a href="<?= base_url('positions/edit/' . $position['id']) ?>" class="btn btn-sm btn-warning" title="Edit Position">
                                    <i class="fas fa-edit"></i>
                                </a>
                                <form action="<?= base_url('positions/delete/' . $position['id']) ?>" method="post" class="d-inline">
                                    <?= csrf_field() ?>
                                    <button type="submit" class="btn btn-sm btn-danger" title="Delete Position" onclick="return confirm('Are you sure you want to delete this position?');">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </form>
                            </td>
                        </tr>
                        <?php endforeach; ?>
                    </tbody>
                </table>
            </div>
        </div>
    </div>
</div>

<!-- CSV Import Modal -->
<div class="modal fade" id="importModal" tabindex="-1" aria-labelledby="importModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-lg">
        <div class="modal-content">
            <div class="modal-header bg-success text-white">
                <h5 class="modal-title" id="importModalLabel">
                    <i class="fas fa-file-csv me-2"></i>Import Positions from CSV
                </h5>
                <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form action="<?= base_url('positions/import-csv') ?>" method="post" enctype="multipart/form-data" id="importForm">
                <?= csrf_field() ?>
                <input type="hidden" name="position_group_id" value="<?= $group['id'] ?>">

                <div class="modal-body">
                    <div class="alert alert-info">
                        <h6><i class="fas fa-info-circle me-2"></i>Import Instructions:</h6>
                        <ul class="mb-0">
                            <li>Download the CSV template first to see the required format</li>
                            <li>Fill in all required fields: Reference, Designation, Classification, Award, Location, Annual Salary, Qualifications, Knowledge, Skills & Competencies, Job Experiences</li>
                            <li>JD files can be uploaded later when editing individual positions</li>
                            <li>Use UTF-8 encoding for special characters</li>
                        </ul>
                    </div>

                    <div class="mb-3">
                        <label for="csv_file" class="form-label">Select CSV File <span class="text-danger">*</span></label>
                        <input type="file" class="form-control" id="csv_file" name="csv_file" accept=".csv" required>
                        <div class="form-text">Only CSV files are allowed. Maximum file size: 5MB</div>
                    </div>

                    <div class="mb-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="skip_duplicates" name="skip_duplicates" value="1" checked>
                            <label class="form-check-label" for="skip_duplicates">
                                Skip duplicate position references
                            </label>
                        </div>
                    </div>

                    <div id="import-progress" class="d-none">
                        <div class="progress mb-3">
                            <div class="progress-bar progress-bar-striped progress-bar-animated" role="progressbar" style="width: 0%"></div>
                        </div>
                        <div class="text-center">
                            <small class="text-muted">Processing CSV file...</small>
                        </div>
                    </div>
                </div>

                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-success" id="importBtn">
                        <i class="fas fa-upload me-2"></i>Import Positions
                    </button>
                </div>
            </form>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    $('#positionsTable').DataTable();

    // Handle CSV import form submission
    $('#importForm').on('submit', function(e) {
        e.preventDefault();

        const fileInput = $('#csv_file')[0];
        if (!fileInput.files.length) {
            alert('Please select a CSV file to import.');
            return;
        }

        // Show progress
        $('#import-progress').removeClass('d-none');
        $('#importBtn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Importing...');

        // Create FormData object
        const formData = new FormData(this);

        // Submit form via AJAX
        $.ajax({
            url: $(this).attr('action'),
            type: 'POST',
            data: formData,
            processData: false,
            contentType: false,
            success: function(response) {
                if (response.success) {
                    alert('Import completed successfully!\n\nImported: ' + response.imported + ' positions\nSkipped: ' + response.skipped + ' positions');
                    location.reload(); // Reload page to show new positions
                } else {
                    alert('Import failed: ' + response.message);
                }
            },
            error: function(xhr) {
                let errorMessage = 'An error occurred during import.';
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    errorMessage = xhr.responseJSON.message;
                }
                alert('Error: ' + errorMessage);
            },
            complete: function() {
                // Hide progress and reset button
                $('#import-progress').addClass('d-none');
                $('#importBtn').prop('disabled', false).html('<i class="fas fa-upload me-2"></i>Import Positions');
                $('#importModal').modal('hide');
            }
        });
    });
});
</script>
<?= $this->endSection() ?>