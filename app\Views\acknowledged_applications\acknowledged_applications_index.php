<?php
/**
 * View file for listing acknowledged applications
 *
 * @var array $applications List of applications that have been acknowledged
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />

    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-check-circle me-2"></i>Acknowledged Applications</h2>
            <p class="text-muted">Applications that have been acknowledged and are active</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <?php if (empty($applications)): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-check-circle fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Acknowledged Applications</h4>
                        <p class="text-muted">No applications have been acknowledged yet.</p>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Acknowledged Applications (<?= count($applications) ?>)</h5>
                    </div>
                    <div class="table-responsive">
                        <table id="acknowledgedApplicationsTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Application #</th>
                                    <th>Applicant Name</th>
                                    <th>Position Applied</th>
                                    <th>Date Applied</th>
                                    <th>Date Acknowledged</th>
                                    <th>Status</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td><?= $app['application_number'] ?></td>
                                        <td><?= $app['fname'] . ' ' . $app['lname'] ?></td>
                                        <td>
                                            <div>
                                                <strong><?= esc($app['position_title']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($app['position_reference']) ?></small>
                                                <br>
                                                <small class="text-info"><?= esc($app['org_name']) ?></small>
                                            </div>
                                        </td>
                                        <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                        <td>
                                            <?php if (!empty($app['received_at'])): ?>
                                                <?= date('d M Y', strtotime($app['received_at'])) ?>
                                                <br>
                                                <small class="text-muted"><?= date('H:i', strtotime($app['received_at'])) ?></small>
                                            <?php else: ?>
                                                <span class="text-muted">N/A</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <span class="badge bg-success">Acknowledged</span>
                                            <?php if (!empty($app['application_status'])): ?>
                                                <br>
                                                <small class="text-muted"><?= ucfirst($app['application_status']) ?></small>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('acknowledged_applications/view/' . $app['id']) ?>"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>

<!-- DataTables CSS and JS -->
<link rel="stylesheet" type="text/css" href="https://cdn.datatables.net/1.11.5/css/dataTables.bootstrap5.min.css">
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/jquery.dataTables.min.js"></script>
<script type="text/javascript" charset="utf8" src="https://cdn.datatables.net/1.11.5/js/dataTables.bootstrap5.min.js"></script>

<script>
$(document).ready(function() {
    // Initialize DataTable for acknowledged applications
    $('#acknowledgedApplicationsTable').DataTable({
        "pageLength": 25,
        "order": [[ 4, "desc" ]], // Order by date acknowledged (descending)
        "columnDefs": [
            { "orderable": false, "targets": [6] } // Disable sorting on Actions column
        ],
        "language": {
            "search": "Search applications:",
            "lengthMenu": "Show _MENU_ applications per page",
            "info": "Showing _START_ to _END_ of _TOTAL_ acknowledged applications",
            "infoEmpty": "No acknowledged applications found",
            "infoFiltered": "(filtered from _MAX_ total applications)",
            "emptyTable": "No acknowledged applications available"
        }
    });
});
</script>

<?= $this->endSection() ?>
