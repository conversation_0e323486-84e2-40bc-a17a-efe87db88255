<?php
/**
 * Modal view for application details
 * 
 * @var array $application Application data to display
 */
?>

<div class="application-details">
    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Application Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Application #:</strong> <?= $application['application_number'] ?></p>
                    <p><strong>Date Applied:</strong> <?= date('d M Y, h:i A', strtotime($application['created_at'])) ?></p>
                </div>
                <div class="col-md-6">
                    <?php if (!empty($application['id_photo_path'])): ?>
                        <div class="text-center">
                            <img src="<?= base_url($application['id_photo_path']) ?>" 
                                 alt="Applicant Photo" class="img-thumbnail" 
                                 style="max-height: 120px;">
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Personal Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Full Name:</strong> <?= $application['fname'] . ' ' . $application['lname'] ?></p>
                    <p><strong>Gender:</strong> <?= $application['gender'] ?? 'Not specified' ?></p>
                    <p><strong>Date of Birth:</strong> <?= !empty($application['dobirth']) ? date('d M Y', strtotime($application['dobirth'])) : 'Not specified' ?></p>
                    <p><strong>Place of Origin:</strong> <?= $application['place_of_origin'] ?? 'Not specified' ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Citizenship:</strong> <?= $application['citizenship'] ?? 'Not specified' ?></p>
                    <p><strong>Marital Status:</strong> <?= $application['marital_status'] ?? 'Not specified' ?></p>
                    <?php if (!empty($application['marital_status']) && strtolower($application['marital_status']) === 'married'): ?>
                        <p><strong>Date of Marriage:</strong> <?= !empty($application['date_of_marriage']) ? date('d M Y', strtotime($application['date_of_marriage'])) : 'Not specified' ?></p>
                        <p><strong>Spouse's Employer:</strong> <?= $application['spouse_employer'] ?? 'Not specified' ?></p>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Contact Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Contact Details:</strong> <?= $application['contact_details'] ?? 'Not specified' ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Location/Address:</strong> <?= $application['location_address'] ?? 'Not specified' ?></p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Employment Information</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Current Employer:</strong> <?= $application['current_employer'] ?? 'Not specified' ?></p>
                    <p><strong>Current Position:</strong> <?= $application['current_position'] ?? 'Not specified' ?></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Current Salary:</strong> <?= !empty($application['current_salary']) ? '$' . number_format($application['current_salary'], 2) : 'Not specified' ?></p>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($application['publications']) || !empty($application['awards'])): ?>
    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Achievements</h5>
            <div class="row">
                <?php if (!empty($application['publications'])): ?>
                <div class="col-md-6">
                    <p><strong>Publications:</strong></p>
                    <div class="bg-light p-2 rounded">
                        <?= nl2br($application['publications']) ?>
                    </div>
                </div>
                <?php endif; ?>
                
                <?php if (!empty($application['awards'])): ?>
                <div class="col-md-6">
                    <p><strong>Awards:</strong></p>
                    <div class="bg-light p-2 rounded">
                        <?= nl2br($application['awards']) ?>
                    </div>
                </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($application['offence_convicted'])): ?>
    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Other Information</h5>
            <p><strong>Offence/Conviction Records:</strong></p>
            <div class="bg-light p-2 rounded">
                <?= nl2br($application['offence_convicted']) ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <?php if (!empty($application['referees'])): ?>
    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">References</h5>
            <div class="bg-light p-2 rounded">
                <?= nl2br($application['referees']) ?>
            </div>
        </div>
    </div>
    <?php endif; ?>

    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Application Status</h5>
            <div class="row">
                <div class="col-md-6">
                    <p><strong>Status:</strong> <span class="badge bg-warning"><?= $application['application_status'] ?? 'Pending' ?></span></p>
                </div>
                <div class="col-md-6">
                    <p><strong>Remarks:</strong> <?= $application['remarks'] ?? 'No remarks provided' ?></p>
                </div>
            </div>
        </div>
    </div>

    <?php if (!empty($application['signature_path'])): ?>
    <div class="row">
        <div class="col-md-12 mb-3">
            <h5 class="border-bottom pb-2">Signature</h5>
            <div class="text-center">
                <img src="<?= base_url($application['signature_path']) ?>" 
                     alt="Applicant Signature" class="img-fluid border p-2" 
                     style="max-height: 80px;">
            </div>
        </div>
    </div>
    <?php endif; ?>
</div> 