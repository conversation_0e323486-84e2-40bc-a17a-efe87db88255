<?php
/**
 * Detailed view for application details
 * 
 * @var array $application Application data to display
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />
    
    <!-- Header with navigation -->
    <div class="row mb-4">
        <div class="col-md-8">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('incoming_applications') ?>">
                            <i class="fas fa-inbox me-1"></i>Incoming Applications
                        </a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        <?= esc($application['application_number']) ?>
                    </li>
                </ol>
            </nav>
            <h2><i class="fas fa-file-alt me-2"></i>Application Details</h2>
            <p class="text-muted">Review application before acknowledging receipt</p>
        </div>
        <div class="col-md-4 text-end">
            <a href="<?= base_url('incoming_applications') ?>" class="btn btn-outline-secondary me-2">
                <i class="fas fa-arrow-left me-1"></i>Back to List
            </a>
            <button type="button" class="btn btn-success acknowledge-btn" data-id="<?= $application['id'] ?>">
                <i class="fas fa-check-circle me-1"></i>Acknowledge Receipt
            </button>
        </div>
    </div>

    <div class="row">
        <!-- Application Information -->
        <div class="col-md-8">
            <!-- Basic Application Info -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>Application Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Application Number:</strong><br><?= esc($application['application_number']) ?></p>
                            <p><strong>Date Applied:</strong><br><?= date('d M Y, h:i A', strtotime($application['created_at'])) ?></p>
                            <p><strong>Status:</strong><br>
                                <?php
                                $statusClass = 'bg-warning';
                                if ($application['application_status'] === 'active') {
                                    $statusClass = 'bg-success';
                                } elseif ($application['application_status'] === 'pending') {
                                    $statusClass = 'bg-warning';
                                } elseif ($application['application_status'] === 'rejected') {
                                    $statusClass = 'bg-danger';
                                }
                                ?>
                                <span class="badge <?= $statusClass ?> fs-6"><?= ucfirst(esc($application['application_status'])) ?></span>
                            </p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Position Applied:</strong><br>
                                <span class="fw-bold text-primary"><?= esc($application['position_title']) ?></span><br>
                                <small class="text-muted"><?= esc($application['position_reference']) ?></small>
                            </p>
                            <p><strong>Department:</strong><br><?= esc($application['department']) ?></p>
                            <p><strong>Organization:</strong><br><?= esc($application['org_name']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Personal Information -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-user me-2"></i>Personal Information</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Full Name:</strong><br><?= esc($application['fname'] . ' ' . $application['lname']) ?></p>
                            <p><strong>Email:</strong><br><?= esc($application['email']) ?></p>
                            <p><strong>Contact Number:</strong><br><?= esc($application['contact_details']) ?></p>
                            <p><strong>Gender:</strong><br><?= esc($application['gender']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Date of Birth:</strong><br><?= date('d M Y', strtotime($application['dobirth'])) ?></p>
                            <p><strong>Place of Origin:</strong><br><?= esc($application['place_of_origin']) ?></p>
                            <p><strong>Current Address:</strong><br><?= esc($application['location_address']) ?></p>
                            <p><strong>Citizenship:</strong><br><?= esc($application['citizenship']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Employment Information -->
            <div class="card mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-briefcase me-2"></i>Current Employment</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Current Employer:</strong><br><?= esc($application['current_employer']) ?></p>
                            <p><strong>Current Position:</strong><br><?= esc($application['current_position']) ?></p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Marital Status:</strong><br><?= esc($application['marital_status']) ?></p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Qualifications & Experience -->
            <div class="card mb-4">
                <div class="card-header bg-warning text-dark">
                    <h5 class="mb-0"><i class="fas fa-graduation-cap me-2"></i>Qualifications & Experience</h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-12">
                            <p><strong>Educational Qualifications:</strong></p>
                            <div class="bg-light p-3 rounded mb-3">
                                <?= nl2br(esc($application['qualifications'] ?? 'No qualifications information available')) ?>
                            </div>

                            <p><strong>Work Experience Summary:</strong></p>
                            <div class="bg-light p-3 rounded">
                                <?= nl2br(esc($application['experience_summary'] ?? 'No work experience summary available')) ?>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Documents -->
            <div class="card mb-4">
                <div class="card-header bg-secondary text-white">
                    <h5 class="mb-0"><i class="fas fa-paperclip me-2"></i>Attached Documents</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($applicationFiles) && count($applicationFiles) > 0): ?>
                        <div class="row">
                            <?php foreach ($applicationFiles as $file): ?>
                                <div class="col-md-6 mb-3">
                                    <div class="border rounded p-3">
                                        <div class="d-flex align-items-center mb-2">
                                            <?php
                                            // Determine file icon based on file extension
                                            $fileExtension = pathinfo($file['file_path'], PATHINFO_EXTENSION);
                                            $iconClass = 'fas fa-file';
                                            $iconColor = 'text-secondary';

                                            switch (strtolower($fileExtension)) {
                                                case 'pdf':
                                                    $iconClass = 'fas fa-file-pdf';
                                                    $iconColor = 'text-danger';
                                                    break;
                                                case 'doc':
                                                case 'docx':
                                                    $iconClass = 'fas fa-file-word';
                                                    $iconColor = 'text-primary';
                                                    break;
                                                case 'jpg':
                                                case 'jpeg':
                                                case 'png':
                                                case 'gif':
                                                    $iconClass = 'fas fa-file-image';
                                                    $iconColor = 'text-success';
                                                    break;
                                                default:
                                                    $iconClass = 'fas fa-file-alt';
                                                    $iconColor = 'text-info';
                                                    break;
                                            }
                                            ?>
                                            <i class="<?= $iconClass ?> fa-2x <?= $iconColor ?> me-3"></i>
                                            <div class="flex-grow-1">
                                                <h6 class="mb-1"><?= esc($file['file_title']) ?></h6>
                                                <?php if (!empty($file['file_description'])): ?>
                                                    <p class="text-muted small mb-1"><?= esc($file['file_description']) ?></p>
                                                <?php endif; ?>
                                                <small class="text-muted">
                                                    <?= strtoupper($fileExtension) ?> File
                                                    <?php if (!empty($file['created_at'])): ?>
                                                        • Uploaded: <?= date('d M Y', strtotime($file['created_at'])) ?>
                                                    <?php endif; ?>
                                                </small>
                                            </div>
                                        </div>
                                        <div class="d-flex gap-2">
                                            <a href="<?= base_url($file['file_path']) ?>" target="_blank" class="btn btn-sm btn-outline-primary flex-fill">
                                                <i class="fas fa-eye me-1"></i>View
                                            </a>
                                            <a href="<?= base_url($file['file_path']) ?>" download class="btn btn-sm btn-outline-secondary flex-fill">
                                                <i class="fas fa-download me-1"></i>Download
                                            </a>
                                        </div>
                                        <?php if (!empty($file['extracted_texts'])): ?>
                                            <div class="mt-2">
                                                <button class="btn btn-sm btn-outline-info w-100" type="button" data-bs-toggle="collapse" data-bs-target="#extractedText<?= $file['id'] ?>" aria-expanded="false">
                                                    <i class="fas fa-file-alt me-1"></i>View Extracted Text
                                                </button>
                                                <div class="collapse mt-2" id="extractedText<?= $file['id'] ?>">
                                                    <div class="card card-body bg-light">
                                                        <small class="text-muted">
                                                            <?= nl2br(esc(substr($file['extracted_texts'], 0, 500))) ?>
                                                            <?php if (strlen($file['extracted_texts']) > 500): ?>
                                                                <span class="text-primary">... (truncated)</span>
                                                            <?php endif; ?>
                                                        </small>
                                                    </div>
                                                </div>
                                            </div>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <div class="text-center py-4">
                            <i class="fas fa-folder-open fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No documents attached to this application.</p>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Applicant Photo -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-camera me-2"></i>Applicant Photo</h6>
                </div>
                <div class="card-body text-center">
                    <?php if (!empty($application['id_photo_path'])): ?>
                        <img src="<?= base_url($application['id_photo_path']) ?>" 
                             alt="Applicant Photo" class="img-fluid rounded mb-3" 
                             style="max-height: 200px;">
                    <?php else: ?>
                        <div class="bg-light rounded d-inline-flex align-items-center justify-content-center mb-3" 
                             style="width: 150px; height: 150px;">
                            <i class="fas fa-user fa-4x text-secondary"></i>
                        </div>
                        <p class="text-muted small">No photo uploaded</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Actions -->
            <div class="card mb-4">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-cogs me-2"></i>Quick Actions</h6>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <?php if ($application['application_status'] !== 'active'): ?>
                            <button type="button" class="btn btn-success acknowledge-btn" data-id="<?= $application['id'] ?>">
                                <i class="fas fa-check-circle me-2"></i>Acknowledge Receipt
                            </button>
                        <?php endif; ?>
                        <a href="<?= base_url('incoming_applications') ?>" class="btn btn-outline-secondary">
                            <i class="fas fa-arrow-left me-2"></i>Back to Applications
                        </a>
                        <button type="button" class="btn btn-outline-info" onclick="window.print()">
                            <i class="fas fa-print me-2"></i>Print Application
                        </button>
                    </div>
                </div>
            </div>

            <!-- Application Timeline -->
            <div class="card">
                <div class="card-header">
                    <h6 class="mb-0"><i class="fas fa-clock me-2"></i>Application Timeline</h6>
                </div>
                <div class="card-body">
                    <div class="timeline">
                        <div class="timeline-item">
                            <div class="timeline-marker bg-primary"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Application Submitted</h6>
                                <p class="timeline-text small text-muted">
                                    <?= date('d M Y, h:i A', strtotime($application['created_at'])) ?>
                                </p>
                            </div>
                        </div>
                        <div class="timeline-item">
                            <div class="timeline-marker bg-warning"></div>
                            <div class="timeline-content">
                                <h6 class="timeline-title">Pending Acknowledgment</h6>
                                <p class="timeline-text small text-muted">
                                    Waiting for receipt acknowledgment
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Handle acknowledge button click
    $(document).on('click', '.acknowledge-btn', function() {
        const id = $(this).data('id');

        if (confirm('Are you sure you want to acknowledge receipt of this application? This will change the status to active and send an email to the applicant.')) {
            acknowledgeApplication(id);
        }
    });

    // Function to acknowledge application
    function acknowledgeApplication(id) {
        // Disable buttons to prevent double submission
        $('.acknowledge-btn').prop('disabled', true).html('<i class="fas fa-spinner fa-spin me-2"></i>Processing...');

        // Directly process acknowledgment without AI profiling
        processAcknowledgment(id);
    }



    // Function to process acknowledgment
    function processAcknowledgment(id) {
        const csrfToken = $('input[name="<?= csrf_token() ?>"]').val();
        const csrfName = '<?= csrf_token() ?>';

        // Create data object
        const data = {};
        data[csrfName] = csrfToken;

        // Update button text
        $('.acknowledge-btn').html('<i class="fas fa-envelope me-2"></i>Sending Email...');

        $.ajax({
            url: `<?= base_url('incoming_applicants/acknowledge') ?>/${id}`,
            type: 'POST',
            data: data,
            dataType: 'json',
            success: function(response) {
                if (response.success) {
                    // Show success message
                    toastr.success('Application acknowledged successfully! Email sent to applicant.');

                    // Update CSRF hash with the new one from the response
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Update the status badge on the page
                    $('.badge').removeClass('bg-warning').addClass('bg-success').text('Active');

                    // Hide the acknowledge button
                    $('.acknowledge-btn').hide();

                    // Show completion message
                    toastr.info('Redirecting to applications list...', '', {timeOut: 2000});

                    // Redirect back to applications list after a short delay
                    setTimeout(function() {
                        window.location.href = '<?= base_url('incoming_applications') ?>';
                    }, 3000);
                } else {
                    // Show error message
                    toastr.error(response.message);

                    // Update CSRF hash if provided
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }

                    // Re-enable the buttons
                    $('.acknowledge-btn').prop('disabled', false).html('<i class="fas fa-check-circle me-2"></i>Acknowledge Receipt');
                }
            },
            error: function(xhr) {
                // Show error message
                toastr.error('An error occurred while processing your request');

                // Try to parse response to get CSRF hash if available
                try {
                    const response = JSON.parse(xhr.responseText);
                    if (response.csrf_hash) {
                        $('input[name="<?= csrf_token() ?>"]').val(response.csrf_hash);
                    }
                } catch (e) {
                    console.error('Could not parse response:', e);
                }

                // Re-enable the buttons
                $('.acknowledge-btn').prop('disabled', false).html('<i class="fas fa-check-circle me-2"></i>Acknowledge Receipt');
            }
        });
    }
});
</script>

<style>
/* Timeline styles */
.timeline {
    position: relative;
    padding-left: 30px;
}

.timeline::before {
    content: '';
    position: absolute;
    left: 15px;
    top: 0;
    bottom: 0;
    width: 2px;
    background: #dee2e6;
}

.timeline-item {
    position: relative;
    margin-bottom: 20px;
}

.timeline-marker {
    position: absolute;
    left: -22px;
    top: 0;
    width: 12px;
    height: 12px;
    border-radius: 50%;
    border: 2px solid #fff;
}

.timeline-title {
    font-size: 14px;
    margin-bottom: 5px;
}

.timeline-text {
    margin-bottom: 0;
}

/* Print styles */
@media print {
    .btn, .card-header, nav, .timeline {
        display: none !important;
    }
    
    .card {
        border: none !important;
        box-shadow: none !important;
    }
}
</style>
<?= $this->endSection() ?>
