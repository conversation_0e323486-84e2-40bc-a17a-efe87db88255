<?php
/**
 * View file for listing unacknowledged applications
 *
 * @var array $applications List of applications pending acknowledgment
 */
?>

<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Hidden CSRF token field for AJAX requests -->
    <input type="hidden" name="<?= csrf_token() ?>" value="<?= csrf_hash() ?>" />

    <div class="row mb-3">
        <div class="col-md-6">
            <h2><i class="fas fa-inbox me-2"></i>Incoming Applications</h2>
            <p class="text-muted">Applications waiting for acknowledgment</p>
        </div>
        <div class="col-md-6 text-end">
            <a href="<?= base_url('dashboard') ?>" class="btn btn-outline-secondary">
                <i class="fas fa-arrow-left me-2"></i>Back to Dashboard
            </a>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <?php if (empty($applications)): ?>
                <div class="card">
                    <div class="card-body text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <h4 class="text-muted">No Incoming Applications</h4>
                        <p class="text-muted">All applications have been acknowledged.</p>
                    </div>
                </div>
            <?php else: ?>
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Applications Pending Acknowledgment (<?= count($applications) ?>)</h5>
                    </div>
                    <div class="table-responsive">
                        <table id="applicationsTable" class="table table-striped table-hover">
                            <thead class="table-dark">
                                <tr>
                                    <th>Application #</th>
                                    <th>Applicant Name</th>
                                    <th>Position Applied</th>
                                    <th>Date Applied</th>
                                    <th>Status</th>
                                    <th width="150">Actions</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($applications as $app): ?>
                                    <tr>
                                        <td><?= $app['application_number'] ?></td>
                                        <td><?= $app['fname'] . ' ' . $app['lname'] ?></td>
                                        <td>
                                            <div>
                                                <strong><?= esc($app['position_title']) ?></strong>
                                                <br>
                                                <small class="text-muted"><?= esc($app['position_reference']) ?></small>
                                                <br>
                                                <small class="text-info"><?= esc($app['org_name']) ?></small>
                                            </div>
                                        </td>
                                        <td><?= date('d M Y', strtotime($app['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-warning">Pending</span>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('incoming_applicants/view/' . $app['id']) ?>"
                                               class="btn btn-sm btn-info">
                                                <i class="fas fa-eye"></i> View
                                            </a>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            <?php endif; ?>
        </div>
    </div>
</div>


<?= $this->endSection() ?>

<?= $this->section('scripts') ?>
<script>
$(document).ready(function() {
    // Initialize DataTable
    const table = $('#applicationsTable').DataTable({
        responsive: true,
        order: [[4, 'desc']], // Sort by date applied desc
        language: {
            search: "Search applications:",
            lengthMenu: "Show _MENU_ applications per page",
            info: "Showing _START_ to _END_ of _TOTAL_ applications",
            emptyTable: "No applications available",
        }
    });




});
</script>
<?= $this->endSection() ?>
