<?= $this->extend('templates/applicants_template') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Welcome Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h1 class="h3 mb-2">Welcome, <?= esc($applicant['first_name']) ?> <?= esc($applicant['last_name']) ?></h1>
                            <p class="text-muted mb-0">Manage your applications and profile</p>
                        </div>
                        <div>
                            <button class="btn btn-outline-primary" onclick="location.href='<?= base_url('applicant/profile') ?>'">
                                <i class="fas fa-user-edit me-2"></i>Edit Profile
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Statistics Cards -->
    <div class="row g-4 mb-4">
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Total Applications</h6>
                            <h3 class="mb-0"><?= $total_applications ?></h3>
                        </div>
                        <div class="bg-primary bg-opacity-10 p-3 rounded">
                            <i class="fas fa-file-alt text-primary"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Pending</h6>
                            <h3 class="mb-0"><?= $pending_applications ?></h3>
                        </div>
                        <div class="bg-warning bg-opacity-10 p-3 rounded">
                            <i class="fas fa-clock text-warning"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Shortlisted</h6>
                            <h3 class="mb-0"><?= $shortlisted_applications ?></h3>
                        </div>
                        <div class="bg-success bg-opacity-10 p-3 rounded">
                            <i class="fas fa-check-circle text-success"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-3">
            <div class="card">
                <div class="card-body">
                    <div class="d-flex justify-content-between align-items-center">
                        <div>
                            <h6 class="text-muted mb-2">Rejected</h6>
                            <h3 class="mb-0"><?= $rejected_applications ?></h3>
                        </div>
                        <div class="bg-danger bg-opacity-10 p-3 rounded">
                            <i class="fas fa-times-circle text-danger"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Profile Completion Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card">
                <div class="card-body p-4">
                    <h5 class="card-title mb-4">Profile Completion</h5>
                    <div class="progress mb-3" style="height: 10px;">
                        <?php
                        $completion = 0;
                        $fields = ['gender', 'dobirth', 'place_of_origin', 'contact_details', 'location_address'];
                        foreach ($fields as $field) {
                            if (!empty($applicant[$field])) {
                                $completion += 20;
                            }
                        }
                        ?>
                        <div class="progress-bar bg-success" role="progressbar" style="width: <?= $completion ?>%"
                             aria-valuenow="<?= $completion ?>" aria-valuemin="0" aria-valuemax="100"></div>
                    </div>
                    <p class="text-muted mb-0">Your profile is <?= $completion ?>% complete. 
                        <?php if ($completion < 100): ?>
                            <a href="<?= base_url('applicant/profile') ?>">Complete your profile</a> to increase your chances of getting hired.
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <!-- Recent Applications -->
        <div class="col-md-8 mb-4">
            <div class="card h-100">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title mb-0">Recent Applications</h5>
                        <a href="<?= base_url('applicant/applications') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <div class="table-responsive">
                        <table class="table table-hover">
                            <thead>
                                <tr>
                                    <th>Position</th>
                                    <th>Department</th>
                                    <th>Applied Date</th>
                                    <th>Status</th>
                                    <th>Action</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php if (empty($recent_applications ?? [])): ?>
                                <tr>
                                    <td colspan="5" class="text-center py-4">No applications found</td>
                                </tr>
                                <?php else: ?>
                                    <?php foreach ($recent_applications as $application): ?>
                                    <tr>
                                        <td><?= esc($application['position_title']) ?></td>
                                        <td><?= esc($application['department']) ?></td>
                                        <td><?= date('M d, Y', strtotime($application['created_at'])) ?></td>
                                        <td>
                                            <span class="badge bg-<?= get_status_color($application['application_status']) ?>">
                                                <?= esc($application['application_status']) ?>
                                            </span>
                                        </td>
                                        <td>
                                            <a href="<?= base_url('applicant/application/' . $application['id']) ?>" 
                                               class="btn btn-sm btn-outline-primary">
                                                View
                                            </a>
                                        </td>
                                    </tr>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>

        <!-- Latest Job Openings -->
        <div class="col-md-4 mb-4">
            <div class="card h-100">
                <div class="card-body p-4">
                    <div class="d-flex justify-content-between align-items-center mb-4">
                        <h5 class="card-title mb-0">Latest Job Openings</h5>
                        <a href="<?= base_url('applicant/jobs') ?>" class="btn btn-sm btn-outline-primary">View All</a>
                    </div>
                    <?php if (empty($latest_jobs ?? [])): ?>
                        <div class="text-center py-4">
                            <i class="fas fa-briefcase fa-3x text-muted mb-3"></i>
                            <p class="text-muted">No job openings available at the moment</p>
                        </div>
                    <?php else: ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($latest_jobs as $job): ?>
                            <a href="<?= base_url('applicant/jobs/view/' . $job['id']) ?>" 
                               class="list-group-item list-group-item-action">
                                <div class="d-flex w-100 justify-content-between">
                                    <h6 class="mb-1"><?= esc($job['title']) ?></h6>
                                    <small class="text-muted"><?= time_elapsed_string($job['posted_date']) ?></small>
                                </div>
                                <p class="mb-1 text-muted small"><?= esc($job['department']) ?></p>
                                <small class="text-primary">
                                    <i class="fas fa-map-marker-alt me-1"></i><?= esc($job['location']) ?>
                                </small>
                            </a>
                            <?php endforeach; ?>
                        </div>
                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->section('scripts') ?>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Add any dashboard-specific JavaScript here
});
</script>
<?= $this->endSection() ?>

<?= $this->endSection() ?> 