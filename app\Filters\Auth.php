<?php

namespace App\Filters;

use CodeIgniter\Filters\FilterInterface;
use CodeIgniter\HTTP\RequestInterface;
use CodeIgniter\HTTP\ResponseInterface;

class Auth implements FilterInterface
{
    public function before(RequestInterface $request, $arguments = null)
    {
        // Get the current URI path for debugging
        $currentPath = $request->getUri()->getPath();
        $currentPath = preg_replace('#^(public/)?(index\.php/)?#', '', $currentPath);
        $currentPath = trim($currentPath, '/');

        // Log the current path for debugging
        log_message('debug', 'Auth Filter: Processing protected path: ' . $currentPath);

        // Check if any arguments were passed for role-based access
        if (!empty($arguments)) {
            // Check for applicant authentication
            if (in_array('applicant', $arguments)) {
                log_message('debug', 'Auth Filter: Checking applicant login for: ' . $currentPath);

                if (!session()->get('applicant_id')) {
                    log_message('debug', 'Auth Filter: No applicant_id in session, redirecting to login');
                    return $this->redirectToLogin($request, 'Please login to continue');
                }

                log_message('debug', 'Auth Filter: Applicant is logged in, allowing access');
                return;
            }

            // Check for admin role-based access
            if (!session()->get('logged_in') || session()->get('role') !== $arguments[0]) {
                log_message('debug', 'Auth Filter: Access denied, role mismatch');
                return $this->redirectToLogin($request, 'Access denied. Login required.');
            }

            log_message('debug', 'Auth Filter: Admin has correct role, allowing access');
            return;
        }

        // Default admin authentication check - this runs for all protected routes
        if (!session()->get('logged_in')) {
            log_message('debug', 'Auth Filter: No admin login, redirecting to home login page');
            return $this->redirectToLogin($request, 'Login required to access this page.');
        }

        log_message('debug', 'Auth Filter: Admin is logged in, allowing access to: ' . $currentPath);
    }



    /**
     * Redirect to login page with appropriate error message
     *
     * @param RequestInterface $request
     * @param string $message Error message to display
     * @return mixed
     */
    private function redirectToLogin(RequestInterface $request, string $message = 'Login required')
    {
        // Handle AJAX requests
        if ($request->header('X-Requested-With') === 'XMLHttpRequest') {
            log_message('debug', 'Auth Filter: AJAX request without authentication, returning 401');

            return service('response')
                ->setJSON([
                    'success' => false,
                    'message' => $message,
                    'redirect' => base_url('login')
                ])
                ->setStatusCode(401);
        }

        // For normal requests, redirect to home login page
        log_message('debug', 'Auth Filter: Redirecting to home login page with message: ' . $message);

        return redirect()
            ->to(base_url('login'))
            ->with('error', $message);
    }

    public function after(RequestInterface $request, ResponseInterface $response, $arguments = null)
    {
        // No after-filter needed
    }
}
