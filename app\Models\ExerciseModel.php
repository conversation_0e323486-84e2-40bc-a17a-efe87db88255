<?php

namespace App\Models;

use CodeIgniter\Model;

class ExerciseModel extends Model
{
    protected $table         = 'exercises';
    protected $primaryKey    = 'id';
    protected $useAutoIncrement = true;
    protected $returnType    = 'array';
    protected $useSoftDeletes = true;
    protected $deletedField = 'deleted_at';
    protected $protectFields = true;

    // Fields that are allowed to be set during insert/update operations.
    protected $allowedFields = [
        'org_id',
        'exercise_name',
        'gazzetted_no',
        'gazzetted_date',
        'advertisement_no',
        'advertisement_date',
        'is_internal',
        'mode_of_advertisement',
        'publish_date_from',
        'publish_date_to',
        'description',
        'pre_screen_criteria',
        'status',
        'created_by',
        'updated_by',
        'deleted_by'
    ];

    // Enable automatic handling of created_at and updated_at fields.
    protected $useTimestamps = true;
    protected $createdField  = 'created_at';
    protected $updatedField  = 'updated_at';
    protected $dateFormat    = 'datetime';

    // Simple validation rules - allow updates with partial data
    protected $validationRules = [
        'org_id'             => 'permit_empty|numeric',
        'exercise_name'      => 'permit_empty|max_length[255]',
        'gazzetted_no'       => 'permit_empty|max_length[255]',
        'gazzetted_date'     => 'permit_empty|valid_date',
        'advertisement_no'   => 'permit_empty|max_length[255]',
        'advertisement_date' => 'permit_empty|valid_date',
        'is_internal'        => 'permit_empty|in_list[0,1]',
        'mode_of_advertisement' => 'permit_empty|max_length[100]',
        'publish_date_from'  => 'permit_empty|valid_date',
        'publish_date_to'    => 'permit_empty|valid_date',
        'status'             => 'permit_empty|in_list[published,draft,selection,archived]',
        'created_by'         => 'permit_empty|numeric',
        'updated_by'         => 'permit_empty|numeric'
    ];

    protected $validationMessages = [
        'org_id' => [
            'required' => 'Organization ID is required',
            'numeric'  => 'Organization ID must be a number'
        ],
        'exercise_name' => [
            'required'   => 'Exercise name is required',
            'max_length' => 'Exercise name cannot exceed 255 characters'
        ],
        'is_internal' => [
            'in_list' => 'Advertisement type must be either internal (1) or external (0)'
        ]
    ];

    protected $skipValidation = false;
    protected $cleanValidationRules = true;

    /**
     * Get exercises by organization ID
     *
     * @param int $orgId
     * @return array
     */
    public function getExercisesByOrgId($orgId)
    {
        return $this->where('org_id', $orgId)->findAll();
    }

    /**
     * Get active exercises (published status)
     *
     * @return array
     */
    public function getActiveExercises()
    {
        return $this->where('status', 'publish')
                    ->where('publish_date_from <=', date('Y-m-d'))
                    ->where('publish_date_to >=', date('Y-m-d'))
                    ->findAll();
    }

    /**
     * Get draft exercises
     *
     * @param int $orgId
     * @return array
     */
    public function getDraftExercises($orgId = null)
    {
        $builder = $this->where('status', 'draft');

        if ($orgId !== null) {
            $builder->where('org_id', $orgId);
        }

        return $builder->findAll();
    }
}